import React from 'react';
import Modal, { ModalSize } from '../../../app/shared/Modal.tsx';
import { EstimateItem } from '../../../../store/claims/types/Claim.ts';
import { Col, Row } from 'antd';
import { formatCurrency } from '../../../../utils/helpers.ts';
import Button from '../../../app/shared/Button.tsx';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onGoBack: VoidFunction;
    onSubmitForApproval: VoidFunction;
    items: EstimateItem[];
}

const EstimateSummaryModal: React.FC<Props> = ({
    items,
    show,
    onClose,
    onGoBack,
    onSubmitForApproval,
}) => {
    const groupedItems = items.reduce<Record<string, EstimateItem[]>>((acc, product) => {
        acc[product.product_family] = acc[product.product_family] || [];
        acc[product.product_family].push(product);
        return acc;
    }, {});

    const calculateSubtotal = (items: EstimateItem[]) => {
        return items.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);
    };

    const calculateTotal = () => {
        return Object.values(groupedItems).reduce((sum, items) => sum + calculateSubtotal(items), 0);
    };

    const renderServiceSection = (category: string, items: EstimateItem[]) => {
        const isStorage = category === 'Storage';
        
        return (
            <div key={category} className="estimate-summary-section">
                <div className="estimate-summary-section-header">
                    {category}
                </div>
                <div className="estimate-summary-table">
                    <Row className="estimate-summary-table-head">
                        <Col span={8}>Service Title</Col>
                        {isStorage && <Col span={4}>Length (Months)</Col>}
                        <Col span={4}>#</Col>
                        <Col span={4}>Rate</Col>
                        <Col span={4} className="end">Total</Col>
                    </Row>
                    <div className="estimate-summary-table-body">
                        {items.map((item, index) => (
                            <Row key={index} className="estimate-summary-table-row">
                                <Col span={8}>{item.product_name}</Col>
                                {isStorage && <Col span={4}>{item.quantity}</Col>}
                                <Col span={4}>{isStorage ? item.quantity : `${item.quantity} hours`}</Col>
                                <Col span={4}>{formatCurrency(item.unit_price)}</Col>
                                <Col span={4} className="end">
                                    {formatCurrency(item.unit_price * item.quantity)}
                                </Col>
                            </Row>
                        ))}
                    </div>
                </div>
                <div className="estimate-summary-subtotal">
                    <span className="estimate-summary-subtotal-label">{category} Subtotal</span>
                    <span className="estimate-summary-subtotal-value">
                        {formatCurrency(calculateSubtotal(items))}
                    </span>
                </div>
            </div>
        );
    };

    return (
        <Modal
            show={show}
            onClose={onClose}
            size={ModalSize.BIG}
            title="Estimate Summary"
            description="Please verify your estimation before sending for Approval."
        >
            <div className="estimate-summary-content">
                {Object.entries(groupedItems).map(([category, items]) =>
                    renderServiceSection(category, items)
                )}
                
                <div className="estimate-summary-total">
                    <span className="estimate-summary-total-label">Total Estimate</span>
                    <span className="estimate-summary-total-value">
                        {formatCurrency(calculateTotal())}
                    </span>
                </div>
            </div>

            <div className="estimate-summary-actions">
                <Button
                    color="default"
                    variant="outlined"
                    onClick={onGoBack}
                    className="estimate-summary-back-button"
                >
                    GO BACK
                </Button>
                <Button
                    color="primary"
                    variant="solid"
                    onClick={onSubmitForApproval}
                    className="estimate-summary-submit-button"
                >
                    SUBMIT FOR APPROVAL
                </Button>
            </div>
        </Modal>
    );
};

export default EstimateSummaryModal; 